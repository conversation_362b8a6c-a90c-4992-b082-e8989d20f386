/**
 * 应用配置模块
 * 集中管理所有配置项，支持多环境配置
 */

import {
    loadEnvironmentConfig,
    getEnvironmentInfo,
    getDatabaseConfig,
    getSecurityConfig,
    getCorsConfig,
    getLogConfig,
} from './env';
import type { AppConfig } from '@/types';

// 加载环境配置
loadEnvironmentConfig();

// 获取环境配置
const envInfo = getEnvironmentInfo();
const dbSettings = getDatabaseConfig();
const securitySettings = getSecurityConfig();
const corsSettings = getCorsConfig();
const logSettings = getLogConfig();

// Application configuration
export const config: AppConfig = {
    server: {
        port: envInfo.port,
        host: envInfo.host,
        env: envInfo.nodeEnv,
    },
    database: {
        host: dbSettings.host,
        port: dbSettings.port,
        database: dbSettings.database,
        user: dbSettings.user,
        password: dbSettings.password,
        ssl: dbSettings.ssl,
        max: dbSettings.maxConnections,
        idleTimeoutMillis: dbSettings.idleTimeout,
        connectionTimeoutMillis: dbSettings.connectionTimeout,
    },
    security: {
        bcryptRounds: securitySettings.bcryptRounds,
        rateLimitWindowMs: securitySettings.rateLimitWindowMs,
        rateLimitMaxRequests: securitySettings.rateLimitMaxRequests,
    },
    cors: {
        origin: corsSettings.origin,
        credentials: corsSettings.credentials,
        ...(corsSettings.methods && { methods: corsSettings.methods }),
        ...(corsSettings.allowedHeaders && { allowedHeaders: corsSettings.allowedHeaders }),
        ...(corsSettings.exposedHeaders && { exposedHeaders: corsSettings.exposedHeaders }),
        ...(corsSettings.maxAge !== undefined && { maxAge: corsSettings.maxAge }),
    },
    logging: {
        level: logSettings.level,
        format: logSettings.format,
    },
    healthCheck: {
        timeout: parseInt(process.env['HEALTH_CHECK_TIMEOUT'] ?? '5000', 10),
    },
    gracefulShutdown: {
        timeout: parseInt(process.env['GRACEFUL_SHUTDOWN_TIMEOUT'] ?? '10000', 10),
    },
    metrics: {
        enabled: process.env['ENABLE_METRICS'] === 'true',
        port: parseInt(process.env['METRICS_PORT'] ?? '9090', 10),
    },
};

// Export individual configurations for convenience
export const {
    server: serverConfig,
    database: databaseConfig,
    security: securityConfig,
    cors: corsConfig,
    logging: loggingConfig,
    healthCheck: healthCheckConfig,
    gracefulShutdown: gracefulShutdownConfig,
    metrics: metricsConfig,
} = config;

// Environment helpers
export const isDevelopment = envInfo.isDevelopment;
export const isProduction = envInfo.isProduction;
export const isTest = envInfo.isTest;

// Database URL construction
export const getDatabaseUrl = (): string => {
    const databaseUrl = process.env['DATABASE_URL'] ?? '';
    if (databaseUrl.trim() !== '') {
        return databaseUrl;
    }

    const { host, port, database, user, password, ssl } = databaseConfig;
    const sslParam = ssl ? '?ssl=true' : '';
    return `postgresql://${user}:${password}@${host}:${port}/${database}${sslParam}`;
};

// Export constants
export * from './constants';

export default config;
