import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { StatusCodes } from 'http-status-codes';
import { MessageService } from '@/services/message';
import { createSuccessResponse, createErrorResponse } from '@/utils';
import { logError } from '@/config/logger';
import type { AuthenticatedRequest, TypedResponse } from '@/types';

export class MessageController {
    /**
     * 根据sessionId获取消息列表（滚动分页）
     * GET /api/message/session/:sessionId
     */
    public static getMessagesBySessionId: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { sessionId } = authReq.params as { sessionId: string };
            const { limit = 20, cursor } = authReq.query as { limit?: number; cursor?: string };
            const userId = authReq.user!.id;

            const result = await MessageService.getMessagesBySessionId(sessionId, userId, limit, cursor);

            authRes.status(StatusCodes.OK).json(
                createSuccessResponse('Messages retrieved successfully', result, authReq.requestId || 'unknown')
            );
        } catch (error) {
            logError('Error getting messages by session ID', {
                requestId: authReq.requestId,
                userId: authReq.user?.id ?? 'unknown',
                sessionId: (authReq.params as { sessionId: string }).sessionId,
            }, error as Error);

            throw error;
        }
    };

    /**
     * 获取用户消息统计信息
     * GET /api/message/stats
     */
    public static getMessageStats: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { timeRange = '30d', includeDetails = false } = authReq.query as {
                timeRange?: string;
                includeDetails?: boolean;
            };
            const userId = authReq.user!.id;

            const stats = await MessageService.getMessageStats(userId, timeRange, includeDetails);

            authRes.status(StatusCodes.OK).json(
                createSuccessResponse(
                    'Message statistics retrieved successfully', 
                    stats, 
                    authReq.requestId || 'unknown'
                )
            );
        } catch (error) {
            logError('Error getting message statistics', {
                requestId: authReq.requestId,
                userId: authReq.user?.id ?? 'unknown',
            }, error as Error);

            throw error;
        }
    };

    /**
     * 根据消息ID获取消息内容
     * GET /api/message/:id
     */
    public static getMessageById: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const { id } = authReq.params as { id: string };
            const userId = authReq.user!.id;

            const message = await MessageService.getMessageById(id, userId);

            if (!message) {
                authRes.status(StatusCodes.NOT_FOUND).json(
                    createErrorResponse(
                        'Not Found', 
                        'Message not found or access denied', 
                        authReq.requestId || 'unknown'
                    )
                );
                return;
            }

            authRes.status(StatusCodes.OK).json(
                createSuccessResponse('Message retrieved successfully', message, authReq.requestId || 'unknown')
            );
        } catch (error) {
            logError('Error getting message by ID', {
                requestId: authReq.requestId,
                userId: authReq.user?.id ?? 'unknown',
                messageId: (authReq.params as { id: string }).id,
            }, error as Error);

            throw error;
        }
    };
}
