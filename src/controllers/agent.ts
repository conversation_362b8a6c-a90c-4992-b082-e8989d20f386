import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { StatusCodes } from 'http-status-codes';

import { AgentService } from '@/services/agent';
import { createSuccessResponse, createErrorResponse, LangfuseTracer } from '@/utils';
import { logInfo, logError } from '@/config/logger';
import type { AuthenticatedRequest, TypedResponse } from '@/types';
import type { GetAgentsQuery, CreateAgent, UpdateAgent } from '@/validators/agent';

export class AgentController {
    /**
     * 获取所有 agents
     * GET /api/agent
     */
    public static getAgents: RequestHandler = async (req, res, next) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const userId = authReq.user!.id;
            const query = authReq.query as unknown as GetAgentsQuery;

            logInfo('Getting all agents', {
                requestId: authReq.requestId,
                userId,
                query,
            });

            // 简化的追踪上下文
            const context = LangfuseTracer.extractContextFromRequest(authReq);
            const agentContext = {
                traceId: context.traceId,
                userId: context.userId,
                sessionId: context.sessionId
            };

            const result = await AgentService.getAllAgents(query, agentContext);

            // 使用工具函数创建响应
            const response = createSuccessResponse(
                'Agents retrieved successfully',
                {
                    agents: result.agents,
                    pagination: {
                        page: result.page,
                        limit: result.limit,
                        total: result.total,
                        totalPages: result.totalPages,
                        hasNext: result.hasNext,
                        hasPrev: result.hasPrev
                    }
                },
                authReq.requestId
            );

            authRes.status(StatusCodes.OK).json(response);
        } catch (error) {
            logError(
                'Failed to get agents',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    query: JSON.stringify(authReq.query),
                },
                error as Error,
            );

            // 简化的错误追踪
            LangfuseTracer.traceError({
                error: error as Error,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
                operation: 'controller.agent.get_agents',
                context: LangfuseTracer.extractContextFromRequest(authReq)
            });

            // 传递错误给全局错误处理器
            next(error);
        }
    };

    /**
     * 根据ID获取单个 agent（不按用户筛选）
     * GET /api/agents/:id
     */
    public static getAgentById: RequestHandler = async (req, res, next) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;

        try {
            const agentId = authReq.params['id'];

            // 简化参数验证
            if (typeof agentId !== 'string' || agentId.trim() === '') {
                authRes.status(StatusCodes.BAD_REQUEST).json(
                    createErrorResponse('Invalid agent ID', 'Agent ID is required', authReq.requestId)
                );
                return;
            }

            const agent = await AgentService.getAgentById(agentId);

            if (!agent) {
                authRes.status(StatusCodes.NOT_FOUND).json(
                    createErrorResponse('Agent not found', 'The requested agent does not exist', authReq.requestId)
                );
                return;
            }

            authRes.status(StatusCodes.OK).json(
                createSuccessResponse('Agent retrieved successfully', agent, authReq.requestId)
            );
        } catch (error) {
            logError('Failed to get agent by ID', {
                requestId: authReq.requestId,
                userId: authReq.user?.id ?? 'unknown',
                agentId: authReq.params['id'] ?? 'unknown',
            }, error as Error);

            next(error);
        }
    };

    /**
     * 创建新的 agent
     * POST /api/agents
     */
    public static createAgent: RequestHandler = async (req, res, next) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;
        const startTime = Date.now();

        // 创建 Langfuse 追踪
        const context = LangfuseTracer.extractContextFromRequest(authReq);
        let trace: unknown = null;

        try {
            const userId = authReq.user!.id;
            const agentData = authReq.body as CreateAgent;

            trace = LangfuseTracer.createOperationTrace(
                'controller.agent.create_agent',
                { agentData },
                {
                    ...context,
                    service: 'agent-controller',
                    operation: 'create_agent'
                }
            );

            // 转换追踪上下文
            const agentContext = {
                traceId: context.traceId,
                userId: context.userId,
                sessionId: context.sessionId
            };

            const newAgent = await AgentService.createAgent(userId, agentData, agentContext);

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                agentId: newAgent.id,
                agentName: newAgent.name,
                agentUrl: newAgent.cardUrl,
                statusCode: StatusCodes.CREATED
            }, {
                success: true,
                duration,
                statusCode: StatusCodes.CREATED
            });

            authRes
                .status(StatusCodes.CREATED)
                .json(
                    createSuccessResponse(
                        'Agent created successfully',
                        newAgent,
                        authReq.requestId,
                    ),
                );
        } catch (error) {
            logError(
                'Failed to create agent',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    agentData: JSON.stringify(authReq.body),
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration,
                error: true,
                errorMessage: (error as Error).message,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR
            });

            LangfuseTracer.traceError({
                error: error as Error,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
                operation: 'controller.agent.create_agent',
                context: {
                    ...context,
                    service: 'agent-controller'
                }
            });

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to create agent',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 更新 agent
     * PUT /api/agents/:id
     */
    public static updateAgent: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;
        const startTime = Date.now();

        // 创建 Langfuse 追踪
        const context = LangfuseTracer.extractContextFromRequest(authReq);
        let trace: unknown = null;

        try {
            const userId = authReq.user!.id;
            const agentId = authReq.params['id'];
            const updateData = authReq.body as UpdateAgent;

            trace = LangfuseTracer.createOperationTrace(
                'controller.agent.update_agent',
                { agentId, updateData },
                {
                    ...context,
                    service: 'agent-controller',
                    operation: 'update_agent'
                }
            );

            if (typeof agentId !== 'string' || agentId.length === 0 || agentId.trim().length === 0) {
                authRes
                    .status(StatusCodes.BAD_REQUEST)
                    .json(
                        createErrorResponse(
                            'Invalid agent ID',
                            'Agent ID is required',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            const updatedAgent = await AgentService.updateAgent(agentId, userId, updateData);

            if (!updatedAgent) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            'Agent not found',
                            'The requested agent does not exist or you do not have permission to update it',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                agentId: updatedAgent.id,
                agentName: updatedAgent.name,
                agentUrl: updatedAgent.cardUrl,
                statusCode: StatusCodes.OK
            }, {
                success: true,
                duration,
                statusCode: StatusCodes.OK
            });

            authRes
                .status(StatusCodes.OK)
                .json(
                    createSuccessResponse(
                        'Agent updated successfully',
                        updatedAgent,
                        authReq.requestId,
                    ),
                );
        } catch (error) {
            logError(
                'Failed to update agent',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    agentId: authReq.params['id'] ?? 'unknown',
                    updateData: JSON.stringify(authReq.body),
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration,
                error: true,
                errorMessage: (error as Error).message,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR
            });

            LangfuseTracer.traceError({
                error: error as Error,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
                operation: 'controller.agent.update_agent',
                context: {
                    ...context,
                    service: 'agent-controller'
                }
            });

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to update agent',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };

    /**
     * 删除 agent
     * DELETE /api/agents/:id
     */
    public static deleteAgent: RequestHandler = async (req, res) => {
        const authReq = req as AuthenticatedRequest;
        const authRes = res as TypedResponse;
        const startTime = Date.now();

        // 创建 Langfuse 追踪
        const context = LangfuseTracer.extractContextFromRequest(authReq);
        let trace: unknown = null;

        try {
            const userId = authReq.user!.id;
            const agentId = authReq.params['id'];

            trace = LangfuseTracer.createOperationTrace(
                'controller.agent.delete_agent',
                { agentId },
                {
                    ...context,
                    service: 'agent-controller',
                    operation: 'delete_agent'
                }
            );

            if (typeof agentId !== 'string' || agentId.length === 0 || agentId.trim().length === 0) {
                authRes
                    .status(StatusCodes.BAD_REQUEST)
                    .json(
                        createErrorResponse(
                            'Invalid agent ID',
                            'Agent ID is required',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            const deleted = await AgentService.deleteAgent(agentId, userId);

            if (!deleted) {
                authRes
                    .status(StatusCodes.NOT_FOUND)
                    .json(
                        createErrorResponse(
                            'Agent not found',
                            'The requested agent does not exist or you do not have permission to delete it',
                            authReq.requestId,
                        ),
                    );
                return;
            }

            // 更新 Langfuse 追踪
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, {
                agentId,
                deleted: true,
                statusCode: StatusCodes.OK
            }, {
                success: true,
                duration,
                statusCode: StatusCodes.OK
            });

            authRes
                .status(StatusCodes.OK)
                .json(
                    createSuccessResponse(
                        'Agent deleted successfully',
                        { id: agentId },
                        authReq.requestId,
                    ),
                );
        } catch (error) {
            logError(
                'Failed to delete agent',
                {
                    requestId: authReq.requestId,
                    userId: authReq.user?.id ?? 'unknown',
                    agentId: authReq.params['id'] ?? 'unknown',
                },
                error as Error,
            );

            // 更新 Langfuse 追踪错误信息
            const duration = Date.now() - startTime;
            LangfuseTracer.updateTrace(trace, { error: (error as Error).message }, {
                success: false,
                duration,
                error: true,
                errorMessage: (error as Error).message,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR
            });

            LangfuseTracer.traceError({
                error: error as Error,
                statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
                operation: 'controller.agent.delete_agent',
                context: {
                    ...context,
                    service: 'agent-controller'
                }
            });

            authRes
                .status(StatusCodes.INTERNAL_SERVER_ERROR)
                .json(
                    createErrorResponse(
                        'Failed to delete agent',
                        'Internal server error',
                        authReq.requestId,
                    ),
                );
        }
    };
}
