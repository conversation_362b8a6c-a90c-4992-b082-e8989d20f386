import { z } from 'zod/v4';
import { messageContentSchema, senderSchema } from './message';
import { basePaginationSchema } from './index';

/**
 * 获取用户所有会话历史记录的查询参数验证
 */
export const getSessionsQuerySchema = basePaginationSchema;

/**
 * 根据agentId获取用户与特定agent聊天会话记录的查询参数验证
 */
export const getSessionsByAgentQuerySchema = basePaginationSchema;

/**
 * Session ID 参数验证
 */
export const sessionParamSchema = z.object({
    id: z.uuid({ message: 'Invalid session ID format' }),
});

/**
 * 可选的 Session ID 参数验证（用于聊天接口）
 */
export const optionalSessionParamSchema = z.object({
    id: z.uuid({ message: 'Invalid session ID format' }).optional(),
});

/**
 * Agent ID 参数验证（用于获取特定agent的会话）
 */
export const sessionAgentIdParamSchema = z.object({
    agentId: z.uuid({ message: 'Invalid agent ID format' }),
});

/**
 * 创建会话的查询参数验证
 */
export const createSessionQuerySchema = z.object({
    agentId: z.uuid({ message: 'Invalid agent ID format' }),
});

/**
 * Session 响应验证
 */
export const sessionResponseSchema = z.object({
    id: z.uuid(),
    title: z.string(),
    userId: z.string(),
    agentId: z.uuid(),
    metadata: z.string().nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
});

/**
 * 分页会话列表响应验证
 */
export const sessionsListResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: z.array(sessionResponseSchema),
    pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        totalPages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
    }),
    timestamp: z.string(),
    requestId: z.uuid(),
});

/**
 * 删除会话响应验证
 */
export const deleteSessionResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: z.object({
        sessionId: z.uuid(),
        deletedMessagesCount: z.number(),
    }),
    timestamp: z.string(),
    requestId: z.uuid(),
});

/**
 * 创建会话响应验证
 */
export const createSessionResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: z.object({
        session: sessionResponseSchema,
    }),
    timestamp: z.string(),
    requestId: z.uuid(),
});

/**
 * 与AI Agent聊天请求验证
 */
export const chatWithAgentSchema = z.object({
    agentId: z.uuid({ message: 'Invalid agent ID format' }),
    message: z.object({
        id: z.string().min(1, 'Message ID is required'),
        role: z.enum(['user', 'assistant'], {
            error: 'Role must be either "user" or "assistant"',
        }),
        content: z.array(messageContentSchema).min(1, 'At least one content item is required'),
        sender: senderSchema.optional(),
    }).optional(),
});

// 导出类型定义
export type GetSessionsQuery = z.infer<typeof getSessionsQuerySchema>;
export type GetSessionsByAgentQuery = z.infer<typeof getSessionsByAgentQuerySchema>;
export type SessionParam = z.infer<typeof sessionParamSchema>;
export type OptionalSessionParam = z.infer<typeof optionalSessionParamSchema>;
export type SessionAgentIdParam = z.infer<typeof sessionAgentIdParamSchema>;
export type CreateSessionQuery = z.infer<typeof createSessionQuerySchema>;
export type SessionResponse = z.infer<typeof sessionResponseSchema>;
export type SessionsListResponse = z.infer<typeof sessionsListResponseSchema>;
export type DeleteSessionResponse = z.infer<typeof deleteSessionResponseSchema>;
export type CreateSessionResponse = z.infer<typeof createSessionResponseSchema>;
export type ChatWithAgentRequest = z.infer<typeof chatWithAgentSchema>;
