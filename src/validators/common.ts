import { z } from 'zod/v4';

// 通用分页查询schema
export const basePaginationSchema = z.object({
    page: z
        .string()
        .optional()
        .transform(val => (val !== undefined && val.trim() !== '' ? parseInt(val, 10) : 1))
        .refine(val => val > 0, { message: 'page must be greater than 0' }),
    limit: z
        .string()
        .optional()
        .transform(val => (val !== undefined && val.trim() !== '' ? parseInt(val, 10) : 20))
        .refine(val => val > 0 && val <= 100, { message: 'limit must be between 1 and 100' }),
    search: z.string().max(255, { message: 'Search query too long' }).optional(),
});

// 通用UUID参数schema
export const uuidParamSchema = z.object({
    id: z.uuid({ error: 'Invalid UUID format' }),
});

// 导出通用类型
export type BasePagination = z.infer<typeof basePaginationSchema>;
export type UuidParam = z.infer<typeof uuidParamSchema>;
