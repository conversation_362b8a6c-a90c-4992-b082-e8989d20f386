import { z } from 'zod/v4';
import { uuidParamSchema } from './common';
import {
    AgentType,
    AgentTarget,
    AgentStatus
} from '@/db/schema/agent';

// Helper functions for type-safe validation
const isValidAgentType = (value: number): boolean => {
    return Object.values(AgentType).includes(value as typeof AgentType[keyof typeof AgentType]);
};

const isValidAgentTarget = (value: number): boolean => {
    return Object.values(AgentTarget).includes(value as typeof AgentTarget[keyof typeof AgentTarget]);
};

const isValidAgentStatus = (value: number): boolean => {
    return Object.values(AgentStatus).includes(value as typeof AgentStatus[keyof typeof AgentStatus]);
};

// Agent 查询参数验证
export const getAgentsQuerySchema = z.object({
    page: z.coerce.number().min(1, { error: 'Page must be at least 1' }).default(1),
    limit: z.coerce
        .number()
        .min(1, { error: 'Limit must be at least 1' })
        .max(100, { error: 'Limit cannot exceed 100' })
        .default(10),
    search: z.string().max(255, { error: 'Search query too long' }).optional(),
    sortBy: z.enum(['name', 'type', 'status', 'createdAt', 'updatedAt']).default('createdAt'),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    type: z.coerce
        .number()
        .refine(val => isValidAgentType(val), {
            message: 'Invalid agent type filter',
        })
        .optional(),
    status: z.coerce
        .number()
        .refine(val => isValidAgentStatus(val), {
            message: 'Invalid status filter',
        })
        .optional(),
    target: z.coerce
        .number()
        .refine(val => isValidAgentTarget(val), {
            message: 'Invalid target filter',
        })
        .optional(),
});

// Agent ID 参数验证 - 使用通用UUID schema
export const agentIdParamSchema = uuidParamSchema;

// 创建 Agent 验证
export const createAgentSchema = z.object({
    name: z
        .string()
        .min(1, { error: 'Agent name is required' })
        .max(100, { error: 'Agent name must be at most 100 characters' })
        .refine(name => name.trim().length > 0, {
            message: 'Agent name cannot be empty or only whitespace',
        }),
    avatar: z.url({ error: 'Invalid avatar URL format' }),
    cardUrl: z.url({ error: 'Invalid card URL format' }),
    type: z
        .number()
        .refine(val => isValidAgentType(val), {
            message: 'Invalid agent type. Must be 1 (创作协作类) or 2 (沉浸式教学类)',
        }),
    target: z
        .number()
        .min(1, { error: 'Target must be at least 1' })
        .max(3, { error: 'Target cannot exceed 3' })
        .refine(val => isValidAgentTarget(val), {
            message: 'Invalid target. Must be 1 (学员), 2 (管理员), or 3 (both)',
        }),
    status: z
        .number()
        .refine(val => isValidAgentStatus(val), {
            message: 'Invalid status. Must be 0 (已删除), 1 (正常), or 2 (下架)',
        })
        .default(AgentStatus.ACTIVE),
    umdUrl: z.url({ error: 'Invalid UMD URL format' }).optional(),
});

// 更新 Agent 验证
export const updateAgentSchema = createAgentSchema.partial();

// Recent session 响应验证
export const recentSessionSchema = z.object({
    id: z.uuid(),
    title: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
    metadata: z.string().nullable(),
});

// Agent 响应验证
export const agentResponseSchema = z.object({
    id: z.uuid(),
    name: z.string(),
    avatar: z.string(),
    cardUrl: z.string(),
    type: z.number(),
    target: z.number(),
    status: z.number(),
    umdUrl: z.string().nullable(),
    userId: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
});

// Agent with recent sessions 响应验证
export const agentWithRecentSessionsSchema = agentResponseSchema.extend({
    recentSessions: z.array(recentSessionSchema),
});

// 分页响应验证
export const agentsListResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: z.array(agentWithRecentSessionsSchema),
    pagination: z.object({
        page: z.number(),
        limit: z.number(),
        total: z.number(),
        totalPages: z.number(),
        hasNext: z.boolean(),
        hasPrev: z.boolean(),
    }),
    timestamp: z.string(),
    requestId: z.uuid(),
});

// 单个 Agent 响应验证
export const agentDetailResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    data: agentResponseSchema,
    timestamp: z.string(),
    requestId: z.uuid(),
});

// 类型导出
export type GetAgentsQuery = z.infer<typeof getAgentsQuerySchema>;
export type AgentIdParam = z.infer<typeof agentIdParamSchema>;
export type CreateAgent = z.infer<typeof createAgentSchema>;
export type UpdateAgent = z.infer<typeof updateAgentSchema>;
export type RecentSession = z.infer<typeof recentSessionSchema>;
export type AgentResponse = z.infer<typeof agentResponseSchema>;
export type AgentWithRecentSessionsResponse = z.infer<typeof agentWithRecentSessionsSchema>;
export type AgentsListResponse = z.infer<typeof agentsListResponseSchema>;
export type AgentDetailResponse = z.infer<typeof agentDetailResponseSchema>;
