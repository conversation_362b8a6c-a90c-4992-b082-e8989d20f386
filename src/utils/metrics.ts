/**
 * 应用指标收集和监控工具
 * 提供性能指标、业务指标和系统指标的收集功能
 */

import { mean, fromPairs } from 'lodash';
import { logInfo } from '@/config/logger';

export interface MetricData {
    name: string;
    value: number;
    timestamp: number;
    tags: Record<string, string> | undefined;
    type: 'counter' | 'gauge' | 'histogram' | 'timer';
}

export interface RequestMetrics {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
}

export interface SystemMetrics {
    memoryUsage: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
    };
    cpuUsage: {
        user: number;
        system: number;
    };
    uptime: number;
    activeConnections: number;
}

export class MetricsCollector {
    private static instance: MetricsCollector | undefined;
    private readonly metrics: Map<string, MetricData[]> = new Map();
    private requestMetrics: {
        total: number;
        successful: number;
        failed: number;
        responseTimes: number[];
        startTime: number;
    } = {
        total: 0,
        successful: 0,
        failed: 0,
        responseTimes: [],
        startTime: Date.now(),
    };

    private constructor() {}

    public static getInstance(): MetricsCollector {
        MetricsCollector.instance ??= new MetricsCollector();
        return MetricsCollector.instance;
    }

    /**
     * 记录计数器指标
     */
    public incrementCounter(name: string, value = 1, tags?: Record<string, string>): void {
        this.recordMetric({
            name,
            value,
            timestamp: Date.now(),
            tags,
            type: 'counter',
        });
    }

    /**
     * 记录仪表盘指标
     */
    public recordGauge(name: string, value: number, tags?: Record<string, string>): void {
        this.recordMetric({
            name,
            value,
            timestamp: Date.now(),
            tags,
            type: 'gauge',
        });
    }

    /**
     * 记录计时器指标
     */
    public recordTimer(name: string, duration: number, tags?: Record<string, string>): void {
        this.recordMetric({
            name,
            value: duration,
            timestamp: Date.now(),
            tags,
            type: 'timer',
        });
    }

    /**
     * 记录直方图指标
     */
    public recordHistogram(name: string, value: number, tags?: Record<string, string>): void {
        this.recordMetric({
            name,
            value,
            timestamp: Date.now(),
            tags,
            type: 'histogram',
        });
    }

    /**
     * 记录请求指标
     */
    public recordRequest(
        responseTime: number,
        success: boolean,
        tags?: Record<string, string>,
    ): void {
        this.requestMetrics.total++;
        this.requestMetrics.responseTimes.push(responseTime);

        if (success) {
            this.requestMetrics.successful++;
            this.incrementCounter('http_requests_successful_total', 1, tags);
        } else {
            this.requestMetrics.failed++;
            this.incrementCounter('http_requests_failed_total', 1, tags);
        }

        this.recordTimer('http_request_duration_ms', responseTime, tags);
        this.incrementCounter('http_requests_total', 1, tags);

        // 保持响应时间数组大小在合理范围内
        if (this.requestMetrics.responseTimes.length > 1000) {
            this.requestMetrics.responseTimes = this.requestMetrics.responseTimes.slice(-500);
        }
    }

    /**
     * 获取请求指标
     */
    public getRequestMetrics(): RequestMetrics {
        const now = Date.now();
        const elapsedSeconds = (now - this.requestMetrics.startTime) / 1000;
        const responseTimes = this.requestMetrics.responseTimes;

        const averageResponseTime = responseTimes.length > 0 ? mean(responseTimes) : 0;

        return {
            totalRequests: this.requestMetrics.total,
            successfulRequests: this.requestMetrics.successful,
            failedRequests: this.requestMetrics.failed,
            averageResponseTime: Math.round(averageResponseTime * 100) / 100,
            requestsPerSecond:
                elapsedSeconds > 0
                    ? Math.round((this.requestMetrics.total / elapsedSeconds) * 100) / 100
                    : 0,
        };
    }

    /**
     * 获取系统指标
     */
    public getSystemMetrics(): SystemMetrics {
        const memUsage = process.memoryUsage();
        const cpuUsage = process.cpuUsage();

        return {
            memoryUsage: {
                rss: Math.round(memUsage.rss / 1024 / 1024), // MB
                heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
                heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
                external: Math.round(memUsage.external / 1024 / 1024), // MB
            },
            cpuUsage: {
                user: Math.round(cpuUsage.user / 1000), // ms
                system: Math.round(cpuUsage.system / 1000), // ms
            },
            uptime: Math.floor(process.uptime()),
            activeConnections: 0, // 这个需要从服务器实例获取
        };
    }

    /**
     * 获取指定指标的数据
     */
    public getMetric(name: string): MetricData[] {
        return this.metrics.get(name) ?? [];
    }

    /**
     * 获取所有指标
     */
    public getAllMetrics(): Record<string, MetricData[]> {
        return fromPairs(Array.from(this.metrics.entries()));
    }

    /**
     * 清理旧指标数据
     */
    public cleanupOldMetrics(maxAge = 3600000): void {
        // 默认1小时
        const cutoff = Date.now() - maxAge;
        let cleanedCount = 0;

        this.metrics.forEach((metricData, name) => {
            const filteredData = metricData.filter(metric => metric.timestamp > cutoff);
            if (filteredData.length !== metricData.length) {
                this.metrics.set(name, filteredData);
                cleanedCount += metricData.length - filteredData.length;
            }
        });

        if (cleanedCount > 0) {
            logInfo('Cleaned up old metrics', { cleanedCount, maxAge });
        }
    }

    /**
     * 启动定期清理
     */
    public startPeriodicCleanup(intervalMs = 300000): void {
        // 默认5分钟
        const cleanupInterval = setInterval(() => {
            this.cleanupOldMetrics();
        }, intervalMs);

        // 注册清理函数
        process.on('exit', () => {
            clearInterval(cleanupInterval);
        });

        logInfo('Started periodic metrics cleanup', { intervalMs });
    }

    /**
     * 记录指标数据
     */
    private recordMetric(metric: MetricData): void {
        const existing = this.metrics.get(metric.name) ?? [];
        existing.push(metric);

        // 限制每个指标的数据点数量
        if (existing.length > 1000) {
            existing.splice(0, existing.length - 500);
        }

        this.metrics.set(metric.name, existing);
    }

    /**
     * 获取指标摘要
     */
    public getMetricsSummary(): {
        totalMetrics: number;
        requestMetrics: RequestMetrics;
        systemMetrics: SystemMetrics;
        topMetrics: Array<{ name: string; count: number; latestValue: number }>;
    } {
        const requestMetrics = this.getRequestMetrics();
        const systemMetrics = this.getSystemMetrics();

        // 获取前10个最活跃的指标
        const topMetrics = Array.from(this.metrics.entries())
            .map(([name, data]) => ({
                name,
                count: data.length,
                latestValue: data.length > 0 ? (data[data.length - 1]?.value ?? 0) : 0,
            }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        return {
            totalMetrics: this.metrics.size,
            requestMetrics,
            systemMetrics,
            topMetrics,
        };
    }

    /**
     * 重置所有指标
     */
    public reset(): void {
        this.metrics.clear();
        this.requestMetrics = {
            total: 0,
            successful: 0,
            failed: 0,
            responseTimes: [],
            startTime: Date.now(),
        };
        logInfo('All metrics have been reset');
    }
}

/**
 * 性能监控装饰器
 */
export function monitor(metricName: string, tags?: Record<string, string>): (
    target: unknown,
    propertyName: string,
    descriptor: PropertyDescriptor
) => PropertyDescriptor {
    return function (_target: unknown, _propertyName: string, descriptor: PropertyDescriptor): PropertyDescriptor {
        const method = descriptor.value as (...args: unknown[]) => Promise<unknown>;

        descriptor.value = async function (...args: unknown[]): Promise<unknown> {
            const startTime = Date.now();
            const collector = MetricsCollector.getInstance();

            try {
                const result = await method.apply(this as unknown, args);
                const duration = Date.now() - startTime;

                collector.recordTimer(metricName, duration, { ...tags, status: 'success' });
                collector.incrementCounter(`${metricName}_success_total`, 1, tags);

                return result;
            } catch (error) {
                const duration = Date.now() - startTime;

                collector.recordTimer(metricName, duration, { ...tags, status: 'error' });
                collector.incrementCounter(`${metricName}_error_total`, 1, tags);

                throw error;
            }
        };

        return descriptor;
    };
}

// 导出单例实例
export const metricsCollector = MetricsCollector.getInstance();
