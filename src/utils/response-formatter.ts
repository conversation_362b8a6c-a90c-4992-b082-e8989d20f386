/**
 * 响应格式化工具
 *
 * 提供统一的API响应格式化功能
 */

import { map, isArray } from 'lodash';

/**
 * 会话数据格式化接口
 */
export interface SessionData {
    id: string;
    title: string;
    userId: string;
    agentId: string;
    metadata: string | null;
    createdAt: Date;
    updatedAt: Date;
}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

/**
 * 格式化的会话响应数据
 */
export interface FormattedSessionData {
    id: string;
    title: string;
    userId: string;
    agentId: string;
    metadata: string | null;
    createdAt: string;
    updatedAt: string;
}

/**
 * 分页响应数据
 */
export interface PaginatedResponse<T = unknown> {
    success: boolean;
    message: string;
    data: T[];
    pagination: PaginationInfo;
    timestamp: string;
    requestId: string;
}

/**
 * 格式化会话数据为API响应格式
 */
export function formatSessionForResponse(session: SessionData): FormattedSessionData {
    return {
        id: session.id,
        title: session.title,
        userId: session.userId,
        agentId: session.agentId,
        metadata: session.metadata,
        createdAt: session.createdAt.toISOString(),
        updatedAt: session.updatedAt.toISOString(),
    };
}

/**
 * 创建标准的分页响应
 */
export function createPaginatedResponse<T = unknown>(
    data: T[],
    pagination: PaginationInfo,
    message: string,
    requestId: string
): PaginatedResponse<T> {
    return {
        success: true,
        message,
        data,
        pagination,
        timestamp: new Date().toISOString(),
        requestId,
    };
}

/**
 * 批量格式化会话数据
 */
export function formatSessionsForResponse(sessions: SessionData[]): FormattedSessionData[] {
    if (!isArray(sessions)) {
        throw new Error('Sessions must be an array');
    }
    return map(sessions, formatSessionForResponse);
}
