import { randomUUID } from 'crypto';

import type { ApiResponse } from '@/types';

/**
 * Generate a UUID v4 (使用Node.js内置方法)
 */
export const generateUUID = (): string => {
    return randomUUID();
};

/**
 * Sleep for specified milliseconds
 */
export const sleep = (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Create a standardized API response
 */
export const createApiResponse = <T>(
    success: boolean,
    message: string,
    data?: T,
    error?: string,
    requestId?: string,
): ApiResponse<T> => {
    return {
        success,
        message,
        ...(data !== undefined && { data }),
        ...(error !== undefined && error.trim().length > 0 && { error }),
        timestamp: new Date().toISOString(),
        requestId: requestId ?? generateUUID(),
    };
};

/**
 * Create a success response
 */
export const createSuccessResponse = <T>(
    message: string,
    data?: T,
    requestId?: string,
): ApiResponse<T> => {
    return createApiResponse(true, message, data, undefined, requestId);
};

/**
 * Create an error response
 */
export const createErrorResponse = (
    message: string,
    error?: string,
    requestId?: string,
): ApiResponse<undefined> => {
    return createApiResponse(false, message, undefined, error, requestId);
};

/**
 * Measure execution time of a function
 */
export const measureTime = async <T>(
    fn: () => Promise<T>,
): Promise<{ result: T; duration: number }> => {
    const start = Date.now();
    const result = await fn();
    const duration = Date.now() - start;

    return { result, duration };
};

// Export utility modules
export * from './errors';
export * from './processHandlers';
export * from './healthCheck';
export * from './session-helpers';
export * from './metrics';
export * from './message-converter';
export * from './response-formatter';
export * from './sse-manager';
export * from './chat-validator';
export * from './langfuse-tracer';
