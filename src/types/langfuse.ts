/**
 * Langfuse 相关类型定义
 *
 * 定义 Langfuse 集成所需的 TypeScript 类型
 */

// 基础更新数据类型
interface BaseUpdateData {
    output?: JsonValue;
    metadata?: JsonObject;
    endTime?: Date;
}

/**
 * Langfuse 追踪状态
 */
export type LangfuseTraceStatus = 'PENDING' | 'COMPLETED' | 'ERROR';

/**
 * Langfuse 事件类型
 */
export type LangfuseEventType = 'trace' | 'generation' | 'span' | 'event';

/**
 * 通用 JSON 值类型
 */
export type JsonValue = string | number | boolean | null | JsonObject | JsonArray;

/**
 * JSON 对象类型
 */
export interface JsonObject {
    [key: string]: JsonValue;
}

/**
 * JSON 数组类型
 */
export type JsonArray = JsonValue[];

/**
 * Langfuse 追踪实例类型（来自 langfuse SDK）
 */
export interface LangfuseTrace {
    id: string;
    update: (data: BaseUpdateData) => void;
    generation: (data: LangfuseGenerationConfig) => LangfuseGeneration;
    span: (data: { name: string; input?: JsonValue; metadata?: JsonObject }) => LangfuseSpan;
    event: (data: LangfuseEventConfig) => void;
    score: (data: LangfuseScoreConfig) => void;
}

/**
 * Langfuse 生成实例类型
 */
export interface LangfuseGeneration {
    id: string;
    update: (data: BaseUpdateData & {
        usage?: {
            promptTokens?: number;
            completionTokens?: number;
            totalTokens?: number;
        };
    }) => void;
}

/**
 * Langfuse Span 实例类型
 */
export interface LangfuseSpan {
    id: string;
    update: (data: BaseUpdateData) => void;
}

/**
 * Langfuse 追踪元数据
 */
export interface LangfuseTraceMetadata {
    /** 服务名称 */
    service?: string;
    /** 版本号 */
    version?: string;
    /** 环境 */
    environment?: string;
    /** 用户 ID */
    userId?: string;
    /** 会话 ID */
    sessionId?: string;
    /** 请求 ID */
    requestId?: string;
    /** 自定义标签 */
    tags?: string[];
    /** 其他元数据 */
    [key: string]: JsonValue;
}

/**
 * Langfuse 追踪输入
 */
export interface LangfuseTraceInput {
    /** HTTP 方法 */
    method?: string;
    /** 请求路径 */
    path?: string;
    /** 请求 URL */
    url?: string;
    /** 查询参数 */
    query?: Record<string, JsonValue>;
    /** 请求体 */
    body?: JsonValue;
    /** 请求头 */
    headers?: Record<string, string>;
    /** 其他输入数据 */
    [key: string]: JsonValue;
}

/**
 * Langfuse 追踪输出
 */
export interface LangfuseTraceOutput {
    /** HTTP 状态码 */
    statusCode?: number;
    /** 状态消息 */
    statusMessage?: string;
    /** 响应体 */
    body?: JsonValue;
    /** 响应头 */
    headers?: Record<string, string>;
    /** 执行时长（毫秒） */
    duration?: number;
    /** 错误信息 */
    error?: string;
    /** 其他输出数据 */
    [key: string]: JsonValue;
}

/**
 * Langfuse 生成配置
 */
export interface LangfuseGenerationConfig {
    /** 生成名称 */
    name: string;
    /** 模型名称 */
    model?: string;
    /** 输入数据 */
    input?: JsonValue;
    /** 输出数据 */
    output?: JsonValue;
    /** 元数据 */
    metadata?: LangfuseTraceMetadata;
    /** 开始时间 */
    startTime?: Date;
    /** 结束时间 */
    endTime?: Date;
    /** 使用情况 */
    usage?: {
        promptTokens?: number;
        completionTokens?: number;
        totalTokens?: number;
    };
}

/**
 * Langfuse 事件配置
 */
export interface LangfuseEventConfig {
    /** 事件名称 */
    name: string;
    /** 输入数据 */
    input?: JsonValue;
    /** 输出数据 */
    output?: JsonValue;
    /** 元数据 */
    metadata?: LangfuseTraceMetadata;
    /** 事件级别 */
    level?: 'DEBUG' | 'DEFAULT' | 'WARNING' | 'ERROR';
}

/**
 * Langfuse 分数配置
 */
export interface LangfuseScoreConfig {
    /** 追踪 ID */
    traceId: string;
    /** 分数名称 */
    name: string;
    /** 分数值 */
    value: number;
    /** 评论 */
    comment?: string;
    /** 数据类型 */
    dataType?: 'NUMERIC' | 'CATEGORICAL' | 'BOOLEAN';
}

// 基础 Langfuse 上下文类型
interface BaseLangfuseContext {
    trace?: LangfuseTrace | undefined;
    traceId?: string | undefined;
    userId?: string | undefined;
    sessionId?: string | undefined;
    agentId?: string | undefined;
}

/**
 * Agent 服务的 Langfuse 追踪上下文
 */
export interface AgentLangfuseContext extends BaseLangfuseContext {}

/**
 * Session 服务的 Langfuse 追踪上下文
 */
export interface SessionLangfuseContext extends BaseLangfuseContext {}

/**
 * Message 服务的 Langfuse 追踪上下文
 */
export interface MessageLangfuseContext extends BaseLangfuseContext {
    /** 消息 ID */
    messageId?: string;
}

/**
 * Langfuse 集成配置
 */
export interface LangfuseIntegrationConfig {
    /** 是否启用 */
    enabled: boolean;
    /** 自动追踪 HTTP 请求 */
    autoTraceRequests?: boolean;
    /** 自动追踪数据库操作 */
    autoTraceDatabase?: boolean;
    /** 自动追踪 Agent 操作 */
    autoTraceAgents?: boolean;
    /** 自动追踪 Session 操作 */
    autoTraceSessions?: boolean;
    /** 自动追踪 Message 操作 */
    autoTraceMessages?: boolean;
    /** 采样率 */
    sampleRate?: number;
    /** 排除的路径 */
    excludePaths?: string[];
}

/**
 * Langfuse 性能指标
 */
export interface LangfuseMetrics {
    /** 总追踪数 */
    totalTraces: number;
    /** 成功追踪数 */
    successfulTraces: number;
    /** 失败追踪数 */
    failedTraces: number;
    /** 平均响应时间 */
    averageResponseTime: number;
    /** 最后更新时间 */
    lastUpdated: Date;
}

/**
 * Langfuse 健康状态
 */
export interface LangfuseHealthStatus {
    /** 是否健康 */
    healthy: boolean;
    /** 状态消息 */
    message: string;
    /** 最后检查时间 */
    lastCheck: Date;
    /** 连接状态 */
    connected: boolean;
    /** 配置状态 */
    configured: boolean;
}

/**
 * Langfuse 工具函数类型
 */
export interface LangfuseUtils {
    /** 创建追踪 */
    createTrace: (name: string, input?: JsonValue, metadata?: LangfuseTraceMetadata) => LangfuseTrace | null;
    /** 创建生成 */
    createGeneration: (config: LangfuseGenerationConfig) => LangfuseGeneration | null;
    /** 创建事件 */
    createEvent: (config: LangfuseEventConfig) => void;
    /** 记录分数 */
    scoreTrace: (config: LangfuseScoreConfig) => void;
    /** 刷新事件 */
    flush: () => Promise<void>;
    /** 获取健康状态 */
    getHealthStatus: () => LangfuseHealthStatus;
    /** 获取性能指标 */
    getMetrics: () => LangfuseMetrics;
}

/**
 * Express Request 扩展已在中间件文件中声明
 * 这里不重复声明以避免类型冲突
 */
