import type { Request, Response } from 'express';

// 通用工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// API响应构建器类型
export type SuccessResponse<T> = ApiResponse<T> & { success: true; error?: never };
export type ErrorResponse = ApiResponse<never> & { success: false; data?: never; error: string };

// Environment types
export interface Environment {
    NODE_ENV: 'development' | 'production' | 'test';
    PORT: number;
    HOST: string;
    DATABASE_URL: string;
    DB_HOST: string;
    DB_PORT: number;
    DB_NAME: string;
    DB_USER: string;
    DB_PASSWORD: string;
    DB_SSL: boolean;
    BCRYPT_ROUNDS: number;
    RATE_LIMIT_WINDOW_MS: number;
    RATE_LIMIT_MAX_REQUESTS: number;
    CORS_ORIGIN: string;
    CORS_CREDENTIALS: boolean;
    LOG_LEVEL: string;
    LOG_FORMAT: string;
    HEALTH_CHECK_TIMEOUT: number;
    GRACEFUL_SHUTDOWN_TIMEOUT: number;
    REDIS_URL?: string;
    REDIS_PASSWORD?: string;
    ENABLE_METRICS: boolean;
    METRICS_PORT: number;
}

// 基础分页信息
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

// 游标分页信息
export interface CursorPaginationInfo {
    limit: number;
    hasNextPage: boolean;
    nextCursor: string | null;
}

// API Response types
export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    timestamp: string;
    requestId: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    pagination: PaginationInfo;
}

export interface CursorPaginatedResponse<T> extends ApiResponse<{
    data: T[];
    pagination: CursorPaginationInfo;
}> {}

// 消息统计响应（使用工具类型简化）
export type MessageStatsResponse = ApiResponse<{
    totalMessages: number;
    userMessages: number;
    assistantMessages: number;
    timeRange: string;
    details?: {
        dailyStats: Array<{
            date: string;
            messageCount: number;
        }>;
    };
}>;

// Error types
export interface ApiError extends Error {
    statusCode: number;
    code?: string;
    details?: unknown;
}

// Request/Response types
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email: string;
        roles: string[];
    };
    requestId: string;
}

export interface TypedResponse<T = unknown> extends Response {
    json: (body: ApiResponse<T>) => this;
}

// Health check types
export interface HealthCheckResult {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: string;
    uptime: number;
    version: string;
    environment: string;
    services: {
        database: ServiceHealth;
        redis?: ServiceHealth;
    };
    memory: {
        used: number;
        total: number;
        percentage: number;
    };
    cpu: {
        usage: number;
    };
}

export interface ServiceHealth {
    status: 'healthy' | 'unhealthy';
    responseTime?: number;
    error?: string;
}

// Database types
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
    ssl: boolean;
    max?: number;
    idleTimeoutMillis?: number;
    connectionTimeoutMillis?: number;
}

// Logging types
export interface LogContext {
    requestId?: string;
    userId?: string;
    method?: string;
    url?: string;
    statusCode?: number;
    duration?: number;
    userAgent?: string;
    ip?: string;
    // Database context
    host?: string;
    port?: number;
    database?: string;
    processId?: number;
    // Additional context
    [key: string]: unknown;
}

// Validation types
export interface ValidationError {
    field: string;
    message: string;
    value?: unknown;
}

// Middleware types
export type AsyncMiddleware = (
    req: AuthenticatedRequest,
    res: TypedResponse,
    next: (error?: Error) => void,
) => Promise<void>;

export type ErrorHandler = (
    error: Error,
    req: AuthenticatedRequest,
    res: TypedResponse,
    next: (error?: Error) => void,
) => void;

// Service types
export interface BaseService {
    name: string;
    initialize(): Promise<void>;
    destroy(): Promise<void>;
    healthCheck(): Promise<ServiceHealth>;
}

// 基础配置类型
interface ServerConfig {
    port: number;
    host: string;
    env: string;
}

interface SecurityConfig {
    bcryptRounds: number;
    rateLimitWindowMs: number;
    rateLimitMaxRequests: number;
}

interface CorsConfig {
    origin: string | string[] | boolean;
    credentials: boolean;
    methods?: string[];
    allowedHeaders?: string[];
    exposedHeaders?: string[];
    maxAge?: number;
}

interface LoggingConfig {
    level: string;
    format: string;
}

interface RedisConfig {
    url: string;
    password?: string;
}

interface MetricsConfig {
    enabled: boolean;
    port: number;
}

// 主配置类型
export interface AppConfig {
    server: ServerConfig;
    database: DatabaseConfig;
    security: SecurityConfig;
    cors: CorsConfig;
    logging: LoggingConfig;
    healthCheck: { timeout: number };
    gracefulShutdown: { timeout: number };
    redis?: RedisConfig;
    metrics: MetricsConfig;
}
