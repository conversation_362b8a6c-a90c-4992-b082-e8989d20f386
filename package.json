{"name": "xui-app-server", "version": "1.0.0", "description": "Modern TypeScript Express application with Drizzle ORM and PostgreSQL", "main": "dist/index.js", "type": "module", "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}, "scripts": {"dev": "cross-env NODE_ENV=development nodemon", "build": "tsc", "start": "node dist/src/index.js", "start:dev": "cross-env NODE_ENV=development node dist/index.js", "start:prod": "cross-env NODE_ENV=production node dist/index.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:generate:dev": "cross-env NODE_ENV=development drizzle-kit generate", "db:migrate:dev": "cross-env NODE_ENV=development drizzle-kit migrate", "db:studio:dev": "cross-env NODE_ENV=development drizzle-kit studio", "db:generate:prod": "cross-env NODE_ENV=production drizzle-kit generate", "db:migrate:prod": "cross-env NODE_ENV=production drizzle-kit migrate", "db:setup": "tsx scripts/db-setup.ts", "dev:setup": "./scripts/dev-setup.sh", "dev:db": "docker-compose -f docker-compose.dev.yml up -d postgres", "dev:db:down": "docker-compose -f docker-compose.dev.yml down", "dev:tools": "docker-compose -f docker-compose.dev.yml --profile tools up -d", "deploy": "./scripts/deploy.sh", "deploy:docker": "./scripts/deploy.sh --target docker", "deploy:pm2": "./scripts/deploy.sh --target pm2", "deploy:compose": "./scripts/deploy.sh --target compose", "deploy:quick": "./scripts/quick-deploy.sh", "deploy:dev": "./scripts/quick-deploy.sh dev", "deploy:staging": "./scripts/quick-deploy.sh staging --pm2", "deploy:production": "./scripts/quick-deploy.sh production --pm2", "deploy:verify": "./scripts/verify-deployment.sh", "docker:build": "docker build -t xui-app-server:latest .", "docker:run": "docker run -p 3000:3000 --env-file .env xui-app-server:latest", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f app", "docker:restart": "docker-compose restart app", "docker:rebuild": "docker-compose up -d --build", "test": "npx tsx scripts/test-runner.ts", "test:unit": "npx tsx scripts/test-runner.ts unit", "test:integration": "npx tsx scripts/test-runner.ts integration", "test:e2e": "npx tsx scripts/test-runner.ts e2e", "test:utils": "npx tsx scripts/test-runner.ts utils", "test:services": "npx tsx scripts/test-runner.ts services", "test:controllers": "npx tsx scripts/test-runner.ts controllers", "test:validators": "npx tsx scripts/test-runner.ts validators", "test:middleware": "npx tsx scripts/test-runner.ts middleware", "test:config": "npx tsx scripts/test-runner.ts config", "test:watch": "npx tsx scripts/test-runner.ts --watch", "test:coverage": "npx tsx scripts/test-runner.ts --coverage", "test:coverage:unit": "npx tsx scripts/test-runner.ts unit --coverage", "langfuse:example": "npx tsx examples/langfuse-usage.ts", "test:jest": "jest", "test:jest:watch": "jest --watch", "test:jest:coverage": "jest --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "pnpm clean", "postbuild": "shx cp -r src/public dist/ || shx echo 'No public directory to copy'"}, "keywords": ["typescript", "express", "drizzle", "postgresql", "zod", "modern", "api"], "author": "", "license": "MIT", "dependencies": {"@a2a-js/sdk": "^0.2.2", "@xui-web-app/a2u": "^0.2.0", "better-sse": "^0.15.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.6.0", "drizzle-orm": "^0.44.2", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "http-status-codes": "^2.3.0", "langfuse": "^3.37.6", "morgan": "^1.10.0", "pg": "^8.16.2", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/morgan": "^1.9.10", "@types/node": "^22.15.33", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "cross-env": "^7.0.3", "drizzle-kit": "^0.31.3", "drizzle-zod": "^0.5.1", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "shx": "^0.4.0", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.0", "pnpm": {"peerDependencyRules": {"ignoreMissing": ["@types/*"]}}}